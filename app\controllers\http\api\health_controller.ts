import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HealthService from 'app/services/HealthService'

export default class HealthController {
  private healthService = new HealthService()

  public async check({ response }: HttpContextContract) {
    const health = await this.healthService.check()

    return response.status(health.status === 'healthy' ? 200 : 503).json(health)
  }

  public async liveness({ response }: HttpContextContract) {
    // Simple liveness probe - just check if app is responding
    return response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
    })
  }

  public async readiness({ response }: HttpContextContract) {
    // Readiness probe - check dependencies
    const health = await this.healthService.check()
    
    return response.status(health.status === 'healthy' ? 200 : 503).json({
      status: health.status,
      timestamp: health.timestamp,
    })
  }
}