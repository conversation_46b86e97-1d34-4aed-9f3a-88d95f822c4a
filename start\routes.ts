/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'

router.get('/', async () => {
  return {
    hello: 'world',
  }
})


// Health check routes
router.get('/health', 'api/HealthController.check')
router.get('/health/liveness', 'api/HealthController.liveness')
router.get('/health/readiness', 'api/HealthController.readiness')