import Database from '@ioc:Adonis/Lucid/Database'
import Redis from '@ioc:Adonis/Addons/Redis'
import { HealthCheckResponse } from 'App/Types/Health'

export default class HealthService {
  public async check(): Promise<HealthCheckResponse> {
    const checks = {
      database: await this.checkDatabase(),
      redis: await this.checkRedis(),
      memory: this.checkMemory(),
      uptime: this.checkUptime(),
    }

    const status = Object.values(checks).every(check => check.status === 'healthy')
      ? 'healthy'
      : 'unhealthy'

    return {
      status,
      timestamp: new Date().toISOString(),
      checks,
    }
  }

  private async checkDatabase(): Promise<{ status: string; message?: string }> {
    try {
      await Database.rawQuery('SELECT 1')
      return { status: 'healthy' }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: error.message,
      }
    }
  }

  private async checkRedis(): Promise<{ status: string; message?: string }> {
    try {
      await Redis.ping()
      return { status: 'healthy' }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: error.message,
      }
    }
  }

  private checkMemory(): { status: string; data: { usage: number; max: number } } {
    const memoryUsage = process.memoryUsage()
    const used = memoryUsage.heapUsed / 1024 / 1024
    const max = memoryUsage.heapTotal / 1024 / 1024

    return {
      status: used / max > 0.9 ? 'warning' : 'healthy',
      data: {
        usage: Math.round(used * 100) / 100,
        max: Math.round(max * 100) / 100,
      },
    }
  }

  private checkUptime(): { status: string; data: { seconds: number } } {
    return {
      status: 'healthy',
      data: {
        seconds: Math.floor(process.uptime()),
      },
    }
  }
}